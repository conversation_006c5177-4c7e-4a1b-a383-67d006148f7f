/**
 * 自定义尺寸图片区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var MediaPlaceholder = blockEditor.MediaPlaceholder;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var SelectControl = components.SelectControl;
    var ToggleControl = components.ToggleControl;
    var Button = components.Button;
    var ButtonGroup = components.ButtonGroup;
    var RangeControl = components.RangeControl;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 预设尺寸选项
    var PRESET_SIZES = [
        { label: '保持原始尺寸', value: 'original' },
        { label: '自定义', value: 'custom' },
        { label: '方形 (1:1)', value: '1:1', width: 400, height: 400 },
        { label: '横向 16:9', value: '16:9', width: 640, height: 360 },
        { label: '横向 4:3', value: '4:3', width: 640, height: 480 },
        { label: '横向 3:2', value: '3:2', width: 600, height: 400 },
        { label: '纵向 2:3', value: '2:3', width: 400, height: 600 },
        { label: '纵向 3:4', value: '3:4', width: 480, height: 640 },
        { label: '纵向 9:16', value: '9:16', width: 360, height: 640 }
    ];

    // 尺寸单位选项
    var SIZE_UNITS = [
        { label: 'px', value: 'px' },
        { label: '%', value: '%' },
        { label: 'vw', value: 'vw' },
        { label: 'vh', value: 'vh' },
        { label: 'em', value: 'em' },
        { label: 'rem', value: 'rem' }
    ];

    // 注册区块
    blocks.registerBlockType('light-fixture/custom-image-block', {
        title: '自定义尺寸图片',
        icon: 'format-image',
        category: 'media',
        description: '添加一个支持自定义尺寸的图片区块，提供完全的尺寸控制。',
        supports: {
            html: false,
            align: ['left', 'center', 'right', 'wide', 'full'],
        },
        attributes: {
            url: {
                type: 'string',
            },
            alt: {
                type: 'string',
                default: '',
            },
            id: {
                type: 'number',
            },
            width: {
                type: 'string',
                default: '400',
            },
            height: {
                type: 'string',
                default: '300',
            },
            widthUnit: {
                type: 'string',
                default: 'px',
            },
            heightUnit: {
                type: 'string',
                default: 'px',
            },
            presetSize: {
                type: 'string',
                default: 'original',
            },
            useOriginalSize: {
                type: 'boolean',
                default: true,
            },
            maintainAspectRatio: {
                type: 'boolean',
                default: false,
            },
            linkUrl: {
                type: 'string',
            },
            linkTarget: {
                type: 'string',
                default: '_self',
            },
            maxWidth: {
                type: 'string',
                default: '',
            },
            maxHeight: {
                type: 'string',
                default: '',
            },
            objectFit: {
                type: 'string',
                default: 'cover',
            },
            className: {
                type: 'string',
            },
        },

        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var isSelected = props.isSelected;

            // 调试：记录区块初始化时的属性
            console.log('🔧 自定义图片区块 - 编辑器初始化，当前属性:', attributes);

            // 处理媒体选择
            function onSelectImage(media) {
                var newAttributes = {
                    url: media.url,
                    id: media.id,
                    alt: media.alt || '',
                };

                // 如果选择了保持原始尺寸，自动设置图片的原始尺寸
                if (attributes.presetSize === 'original' || attributes.useOriginalSize) {
                    if (media.width && media.height) {
                        newAttributes.width = media.width.toString();
                        newAttributes.height = media.height.toString();
                        newAttributes.widthUnit = 'px';
                        newAttributes.heightUnit = 'px';
                        newAttributes.useOriginalSize = true;
                    }
                }

                // 调试：记录属性设置
                console.log('🖼️ 自定义图片区块 - 设置属性:', newAttributes);
                setAttributes(newAttributes);
            }

            // 处理预设尺寸变更
            function onPresetSizeChange(value) {
                setAttributes({ presetSize: value });

                if (value === 'original') {
                    // 保持原始尺寸
                    setAttributes({ useOriginalSize: true });
                    // 如果已有图片，获取其原始尺寸
                    if (attributes.id && wp.media) {
                        var attachment = wp.media.attachment(attributes.id);
                        if (attachment.get('width') && attachment.get('height')) {
                            setAttributes({
                                width: attachment.get('width').toString(),
                                height: attachment.get('height').toString(),
                                widthUnit: 'px',
                                heightUnit: 'px',
                            });
                        }
                    }
                } else if (value !== 'custom') {
                    setAttributes({ useOriginalSize: false });
                    var preset = PRESET_SIZES.find(function(size) {
                        return size.value === value;
                    });
                    if (preset && preset.width && preset.height) {
                        setAttributes({
                            width: preset.width.toString(),
                            height: preset.height.toString(),
                            widthUnit: 'px',
                            heightUnit: 'px',
                        });
                    }
                } else {
                    setAttributes({ useOriginalSize: false });
                }
            }

            // 处理宽度变更
            function onWidthChange(value) {
                setAttributes({ width: value });
                
                if (attributes.maintainAspectRatio && attributes.height && value) {
                    var aspectRatio = parseFloat(attributes.width) / parseFloat(attributes.height);
                    var newHeight = Math.round(parseFloat(value) / aspectRatio);
                    setAttributes({ height: newHeight.toString() });
                }
            }

            // 处理高度变更
            function onHeightChange(value) {
                setAttributes({ height: value });
                
                if (attributes.maintainAspectRatio && attributes.width && value) {
                    var aspectRatio = parseFloat(attributes.width) / parseFloat(attributes.height);
                    var newWidth = Math.round(parseFloat(value) * aspectRatio);
                    setAttributes({ width: newWidth.toString() });
                }
            }

            var blockProps = useBlockProps({
                className: 'custom-image-block-editor'
            });

            // 侧边栏控件
            var inspectorControls = el(InspectorControls, {},
                // 尺寸设置面板
                el(PanelBody, { title: '尺寸设置', initialOpen: true }, [
                    // 预设尺寸选择
                    el(SelectControl, {
                        label: '预设尺寸',
                        value: attributes.presetSize,
                        options: PRESET_SIZES,
                        onChange: onPresetSizeChange,
                    }),
                    
                    // 显示原始尺寸信息（当选择保持原始尺寸时）
                    attributes.presetSize === 'original' && attributes.url && el('div', {
                        className: 'original-size-info',
                        style: {
                            padding: '12px',
                            background: '#f0f6fc',
                            border: '1px solid #c3e4f7',
                            borderRadius: '4px',
                            marginBottom: '16px'
                        }
                    }, [
                        el('p', {
                            style: { margin: '0', fontSize: '14px', color: '#0073aa' }
                        }, '📏 图片将以原始尺寸显示'),
                        attributes.width && attributes.height && el('p', {
                            style: { margin: '4px 0 0 0', fontSize: '12px', color: '#666' }
                        }, '原始尺寸: ' + attributes.width + '×' + attributes.height + ' 像素')
                    ]),

                    // 自定义宽度（非原始尺寸时显示）
                    attributes.presetSize !== 'original' && el('div', { className: 'custom-size-control' }, [
                        el('label', { className: 'components-base-control__label' }, '宽度'),
                        el('div', { className: 'custom-size-input-group' }, [
                            el(TextControl, {
                                value: attributes.width,
                                onChange: onWidthChange,
                                type: 'number',
                                min: '0',
                            }),
                            el(SelectControl, {
                                value: attributes.widthUnit,
                                options: SIZE_UNITS,
                                onChange: function(value) {
                                    setAttributes({ widthUnit: value });
                                },
                            }),
                        ]),
                    ]),

                    // 自定义高度（非原始尺寸时显示）
                    attributes.presetSize !== 'original' && el('div', { className: 'custom-size-control' }, [
                        el('label', { className: 'components-base-control__label' }, '高度'),
                        el('div', { className: 'custom-size-input-group' }, [
                            el(TextControl, {
                                value: attributes.height,
                                onChange: onHeightChange,
                                type: 'number',
                                min: '0',
                            }),
                            el(SelectControl, {
                                value: attributes.heightUnit,
                                options: SIZE_UNITS,
                                onChange: function(value) {
                                    setAttributes({ heightUnit: value });
                                },
                            }),
                        ]),
                    ]),
                    
                    // 保持纵横比（非原始尺寸时显示）
                    attributes.presetSize !== 'original' && el(ToggleControl, {
                        label: '保持纵横比',
                        checked: attributes.maintainAspectRatio,
                        onChange: function(value) {
                            setAttributes({ maintainAspectRatio: value });
                        },
                    }),
                ]),

                // 高级设置面板
                el(PanelBody, { title: '高级设置', initialOpen: false }, [
                    // 最大宽度
                    el(TextControl, {
                        label: '最大宽度 (可选)',
                        value: attributes.maxWidth,
                        onChange: function(value) {
                            setAttributes({ maxWidth: value });
                        },
                        help: '限制图片的最大宽度，例如：800px 或 100%',
                    }),

                    // 最大高度
                    el(TextControl, {
                        label: '最大高度 (可选)',
                        value: attributes.maxHeight,
                        onChange: function(value) {
                            setAttributes({ maxHeight: value });
                        },
                        help: '限制图片的最大高度，例如：600px 或 50vh',
                    }),

                    // 对象适应方式
                    el(SelectControl, {
                        label: '图片适应方式',
                        value: attributes.objectFit,
                        options: [
                            { label: '覆盖 (Cover)', value: 'cover' },
                            { label: '包含 (Contain)', value: 'contain' },
                            { label: '填充 (Fill)', value: 'fill' },
                            { label: '缩小 (Scale Down)', value: 'scale-down' },
                            { label: '无 (None)', value: 'none' },
                        ],
                        onChange: function(value) {
                            setAttributes({ objectFit: value });
                        },
                        help: '控制图片如何适应指定的尺寸',
                    }),
                ]),

                // 图片设置面板
                el(PanelBody, { title: '图片设置', initialOpen: false }, [
                    // Alt文本
                    el(TextControl, {
                        label: 'Alt文本',
                        value: attributes.alt,
                        onChange: function(value) {
                            setAttributes({ alt: value });
                        },
                        help: '为屏幕阅读器和SEO提供图片描述',
                    }),

                    // 链接URL
                    el(TextControl, {
                        label: '链接URL (可选)',
                        value: attributes.linkUrl,
                        onChange: function(value) {
                            setAttributes({ linkUrl: value });
                        },
                        help: '点击图片时跳转的链接',
                    }),

                    // 链接目标
                    attributes.linkUrl && el(SelectControl, {
                        label: '链接打开方式',
                        value: attributes.linkTarget,
                        options: [
                            { label: '当前窗口', value: '_self' },
                            { label: '新窗口', value: '_blank' },
                        ],
                        onChange: function(value) {
                            setAttributes({ linkTarget: value });
                        },
                    }),

                    // 更换图片按钮
                    attributes.url && el(MediaUploadCheck, {},
                        el(MediaUpload, {
                            onSelect: onSelectImage,
                            allowedTypes: ['image'],
                            value: attributes.id,
                            render: function(obj) {
                                return el(Button, {
                                    onClick: obj.open,
                                    isSecondary: true,
                                }, '更换图片');
                            },
                        })
                    ),

                    // 移除图片按钮
                    attributes.url && el(Button, {
                        onClick: function() {
                            setAttributes({
                                url: undefined,
                                id: undefined,
                                alt: '',
                            });
                        },
                        isDestructive: true,
                    }, '移除图片'),
                ])
            );

            // 计算图片样式
            function getImageStyle() {
                var style = {};

                // 如果选择保持原始尺寸，不设置宽高样式，让图片以原始尺寸显示
                if (attributes.presetSize === 'original' || attributes.useOriginalSize) {
                    // 只设置最大尺寸限制，让图片保持原始比例
                    if (attributes.maxWidth) {
                        style.maxWidth = attributes.maxWidth;
                    }
                    if (attributes.maxHeight) {
                        style.maxHeight = attributes.maxHeight;
                    }
                    // 确保图片不会超出容器
                    if (!attributes.maxWidth && !attributes.maxHeight) {
                        style.maxWidth = '100%';
                        style.height = 'auto';
                    }
                } else {
                    // 自定义尺寸模式
                    if (attributes.width && attributes.widthUnit) {
                        style.width = attributes.width + attributes.widthUnit;
                    }

                    if (attributes.height && attributes.heightUnit) {
                        style.height = attributes.height + attributes.heightUnit;
                    }

                    if (attributes.maxWidth) {
                        style.maxWidth = attributes.maxWidth;
                    }

                    if (attributes.maxHeight) {
                        style.maxHeight = attributes.maxHeight;
                    }

                    if (attributes.objectFit) {
                        style.objectFit = attributes.objectFit;
                    }
                }

                return style;
            }

            // 如果没有选择图片，显示媒体占位符
            if (!attributes.url) {
                return el('div', blockProps,
                    inspectorControls,
                    el(MediaPlaceholder, {
                        icon: 'format-image',
                        labels: {
                            title: '自定义尺寸图片',
                            instructions: '上传图片或从媒体库中选择一张图片。',
                        },
                        onSelect: onSelectImage,
                        accept: 'image/*',
                        allowedTypes: ['image'],
                    })
                );
            }

            // 渲染图片预览
            var imageElement = el('img', {
                src: attributes.url,
                alt: attributes.alt,
                style: getImageStyle(),
                className: 'custom-image-preview',
            });

            // 如果有链接，包装在链接中
            if (attributes.linkUrl) {
                imageElement = el('a', {
                    href: attributes.linkUrl,
                    target: attributes.linkTarget,
                    rel: attributes.linkTarget === '_blank' ? 'noopener noreferrer' : undefined,
                }, imageElement);
            }

            return el('div', blockProps,
                inspectorControls,
                el('figure', { className: 'custom-image-figure' },
                    imageElement,
                    // 显示尺寸信息
                    isSelected && el('div', { className: 'custom-image-size-info' },
                        attributes.presetSize === 'original'
                            ? '原始尺寸: ' + (attributes.width || '自动') + '×' + (attributes.height || '自动') + ' 像素'
                            : '尺寸: ' + attributes.width + attributes.widthUnit + ' × ' + attributes.height + attributes.heightUnit
                    )
                )
            );
        },

        // 保存函数 - 确保属性正确序列化
        save: function(props) {
            // 对于动态区块，返回null让WordPress处理序列化
            // 但确保所有属性都已正确设置
            return null;
        }
    });
})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
